<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Forgot Password - mermantic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <header>
    <div class="container">
      <h1>mermantic</h1>
      <nav>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/login.html">Login</a></li>
          <li><a href="/register.html">Register</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <main class="container">
    <section class="auth-form">
      <h2>Reset Your Password</h2>
      <p>Enter your email address and we'll send you a link to reset your password.</p>
      
      <div id="error-message" class="error-message"></div>
      <div id="success-message" class="success-message" style="display: none;"></div>

      <form id="forgot-password-form">
        <div class="form-group">
          <label for="email">Email Address</label>
          <input type="email" id="email" name="email" class="nui-input" required>
        </div>

        <div class="form-actions">
          <button type="submit" class="nui-button primary">Send Reset Link</button>
        </div>
      </form>

      <p class="auth-redirect">
        Remember your password? <a href="/login.html">Back to Login</a>
      </p>
    </section>
  </main>

  <footer>
    <div class="container">
      <p>&copy; 2023 mermantic. All rights reserved.</p>
    </div>
  </footer>

  <script src="/nui/nui.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const forgotPasswordForm = document.getElementById('forgot-password-form');
      const errorMessage = document.getElementById('error-message');
      const successMessage = document.getElementById('success-message');

      forgotPasswordForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const email = document.getElementById('email').value;

        // Hide previous messages
        errorMessage.style.display = 'none';
        successMessage.style.display = 'none';

        try {
          const response = await fetch('/api/users/forgot-password', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email })
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.message || 'Failed to send reset email');
          }

          // Show success message
          successMessage.textContent = data.message;
          successMessage.style.display = 'block';
          
          // Clear the form
          forgotPasswordForm.reset();

        } catch (error) {
          errorMessage.textContent = error.message;
          errorMessage.style.display = 'block';
        }
      });
    });
  </script>
</body>
</html>
