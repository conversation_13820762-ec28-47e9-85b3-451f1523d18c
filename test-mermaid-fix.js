// Test script to verify the Mermaid Unicode fix
// This can be run in the browser console on the dashboard page

function testMermaidUnicodeFix() {
    console.log('🧪 Testing Mermaid Unicode Character Fix...');
    
    // Test cases with problematic Unicode characters
    const testCases = [
        {
            name: 'Plus/Minus Emojis',
            content: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| f1[➕]
    B -->|No| f2[➖]
    f1 --- f2
    end`
        },
        {
            name: 'Various Emojis',
            content: `graph LR
    A[🔥 Fire] --> B[💡 Idea]
    B --> C[📊 Chart]
    C --> D[⭐ Star]`
        },
        {
            name: 'Mixed Unicode',
            content: `flowchart TD
    A[✓ Valid] --> B[❌ Invalid]
    B --> C[⚠ Warning]
    C --> D[🎯 Target]`
        }
    ];
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    testCases.forEach((testCase, index) => {
        console.log(`\n📋 Test ${index + 1}: ${testCase.name}`);
        console.log('Original content:', testCase.content);
        
        try {
            // Test original content (should fail)
            try {
                mermaid.parse(testCase.content);
                console.log('⚠️ Original content unexpectedly passed');
            } catch (originalError) {
                console.log('❌ Original content failed as expected:', originalError.message);
            }
            
            // Test sanitized content (should pass)
            const sanitizationResult = sanitizeMermaidContent(testCase.content);
            console.log('Sanitized content:', sanitizationResult.content);
            console.log('Has replacements:', sanitizationResult.hasReplacements);
            
            mermaid.parse(sanitizationResult.content);
            console.log('✅ Sanitized content passed!');
            passedTests++;
            
        } catch (error) {
            console.log('❌ Test failed:', error.message);
        }
    });
    
    console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! The Unicode fix is working correctly.');
    } else {
        console.log('⚠️ Some tests failed. The fix may need adjustment.');
    }
    
    return {
        passed: passedTests,
        total: totalTests,
        success: passedTests === totalTests
    };
}

// Function to test the sanitization function specifically
function testSanitizationFunction() {
    console.log('\n🔧 Testing sanitization function...');
    
    const testInputs = [
        { input: 'f1[➕]', expected: 'f1[+]' },
        { input: 'f2[➖]', expected: 'f2[-]' },
        { input: 'A[🔥 Fire]', expected: 'A[fire Fire]' },
        { input: 'B[✓ Valid]', expected: 'B[check Valid]' },
        { input: 'C[❌ Invalid]', expected: 'C[x Invalid]' }
    ];
    
    testInputs.forEach((test, index) => {
        const result = sanitizeMermaidContent(test.input);
        const passed = result.content.includes(test.expected.split('[')[1].split(']')[0]);
        console.log(`Test ${index + 1}: ${test.input} → ${result.content} ${passed ? '✅' : '❌'}`);
    });
}

// Export for use in browser console
if (typeof window !== 'undefined') {
    window.testMermaidUnicodeFix = testMermaidUnicodeFix;
    window.testSanitizationFunction = testSanitizationFunction;
}

console.log('🚀 Test functions loaded. Run testMermaidUnicodeFix() to test the fix.');
