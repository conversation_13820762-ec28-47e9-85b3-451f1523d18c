const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const dbPath = path.join(__dirname, '../database.json');

// Initialize database file if it doesn't exist
function initDatabase() {
  if (!fs.existsSync(dbPath)) {
    const initialData = {
      users: [],
      charts: []
    };
    fs.writeFileSync(dbPath, JSON.stringify(initialData, null, 2));
  }
}

// Read database
function readDatabase() {
  try {
    const data = fs.readFileSync(dbPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading database:', error);
    initDatabase();
    return { users: [], charts: [] };
  }
}

// Write database
function writeDatabase(data) {
  try {
    fs.writeFileSync(dbPath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing database:', error);
    throw error;
  }
}

// Database operations
const db = {
  // Run a callback with database data
  run: function(sql, params, callback) {
    // This is a simplified implementation for basic operations
    console.log('DB operation:', sql, params);
    if (callback) callback(null);
  },

  // Get a single record
  get: function(sql, params, callback) {
    const data = readDatabase();

    // Simple SQL parsing for basic operations
    if (sql.includes('SELECT * FROM users WHERE username = ?')) {
      const user = data.users.find(u => u.username === params[0]);
      callback(null, user);
    } else if (sql.includes('SELECT * FROM users WHERE email = ?')) {
      const user = data.users.find(u => u.email === params[0]);
      callback(null, user);
    } else if (sql.includes('SELECT * FROM users WHERE id = ?')) {
      const user = data.users.find(u => u.id === params[0]);
      callback(null, user);
    } else if (sql.includes('SELECT * FROM users WHERE reset_token = ?')) {
      const user = data.users.find(u => u.reset_token === params[0] && new Date(u.reset_token_expires) > new Date());
      callback(null, user);
    } else {
      callback(null, null);
    }
  },

  // Get all records
  all: function(sql, params, callback) {
    // Handle case where params is actually the callback
    if (typeof params === 'function') {
      callback = params;
      params = [];
    }

    const data = readDatabase();

    if (sql.includes('SELECT * FROM users')) {
      callback(null, data.users.slice(0, 5)); // Limit to 5 for sample
    } else {
      callback(null, []);
    }
  },

  // Serialize operations
  serialize: function(callback) {
    callback();
  }
};

// Mock functions for user operations
db.createUser = function(username, email, hashedPassword, callback) {
  const data = readDatabase();
  const newUser = {
    id: data.users.length + 1,
    username,
    email,
    password: hashedPassword,
    auth_type: 'local',
    reset_token: null,
    reset_token_expires: null,
    created_at: new Date().toISOString()
  };
  data.users.push(newUser);
  writeDatabase(data);
  callback(null, newUser.id);
};

db.updateUserResetToken = function(email, resetToken, expires, callback) {
  const data = readDatabase();
  const user = data.users.find(u => u.email === email);
  if (user) {
    user.reset_token = resetToken;
    user.reset_token_expires = expires;
    writeDatabase(data);
    callback(null);
  } else {
    callback(new Error('User not found'));
  }
};

db.resetUserPassword = function(userId, hashedPassword, callback) {
  const data = readDatabase();
  const user = data.users.find(u => u.id === userId);
  if (user) {
    user.password = hashedPassword;
    user.reset_token = null;
    user.reset_token_expires = null;
    writeDatabase(data);
    callback(null);
  } else {
    callback(new Error('User not found'));
  }
};

// Initialize database on startup
initDatabase();

console.log('Using JSON file database at:', dbPath);

module.exports = db;
