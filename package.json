{"name": "mermantic", "version": "1.0.0", "description": "A web application for creating and sharing Mermaid diagrams", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mermaid", "diagrams", "charts"], "author": "", "license": "MIT", "dependencies": {"bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-session": "^1.17.3", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "resend": "^4.5.1", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.1.10"}}