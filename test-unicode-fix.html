<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Unicode Fix</title>
    <script src="public/js/mermaid.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>Mermaid Unicode Character Test</h1>
    
    <div class="test-container">
        <h2>Test 1: Original Problematic Diagram</h2>
        <div id="original-diagram">
            <pre>graph TD
    A[Start] --> B{Decision}
    B -->|Yes| f1[➕]
    B -->|No| f2[➖]
    f1 --- f2
    end</pre>
        </div>
        <div id="original-result"></div>
    </div>

    <div class="test-container">
        <h2>Test 2: Sanitized Diagram</h2>
        <div id="sanitized-diagram"></div>
        <div id="sanitized-result"></div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: false,
            securityLevel: 'loose',
            theme: 'default'
        });

        // Function to sanitize Mermaid content for problematic Unicode characters
        function sanitizeMermaidContent(content) {
            if (!content) return content;
            
            // Map of problematic Unicode characters to safe alternatives
            const unicodeReplacements = {
                '➕': '+',
                '➖': '-',
                '✓': 'check',
                '✗': 'x',
                '⚠': 'warn',
                '⭐': 'star',
                '🔥': 'fire',
                '💡': 'idea',
                '📊': 'chart',
                '📈': 'up',
                '📉': 'down',
                '🎯': 'target',
                '⚡': 'fast',
                '🔒': 'lock',
                '🔓': 'unlock',
                '❌': 'x',
                '⭕': 'o',
                '🟢': 'green',
                '🔴': 'red',
                '🟡': 'yellow',
                '🔵': 'blue',
                '⚫': 'black',
                '⚪': 'white'
            };
            
            let sanitized = content;
            let hasReplacements = false;
            
            // Replace problematic Unicode characters
            for (const [unicode, replacement] of Object.entries(unicodeReplacements)) {
                if (sanitized.includes(unicode)) {
                    sanitized = sanitized.replace(new RegExp(unicode, 'g'), replacement);
                    hasReplacements = true;
                }
            }
            
            // Check for other potentially problematic Unicode characters
            const problematicUnicodeRegex = /[\u2000-\u2BFF\u1F000-\u1F9FF]/g;
            const problematicChars = content.match(problematicUnicodeRegex);
            
            if (problematicChars && problematicChars.length > 0) {
                // Replace remaining problematic characters with safe alternatives
                sanitized = sanitized.replace(problematicUnicodeRegex, '?');
                hasReplacements = true;
            }
            
            return {
                content: sanitized,
                hasReplacements: hasReplacements,
                originalContent: content
            };
        }

        // Test the original problematic diagram
        const originalContent = `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| f1[➕]
    B -->|No| f2[➖]
    f1 --- f2
    end`;

        // Test 1: Try to render original content
        try {
            mermaid.parse(originalContent);
            document.getElementById('original-result').innerHTML = '<p class="success">✓ Original content parsed successfully!</p>';
        } catch (error) {
            document.getElementById('original-result').innerHTML = `<p class="error">✗ Original content failed: ${error.message}</p>`;
        }

        // Test 2: Try sanitized content
        const sanitizationResult = sanitizeMermaidContent(originalContent);
        const sanitizedContent = sanitizationResult.content;

        document.getElementById('sanitized-diagram').innerHTML = `<pre>${sanitizedContent}</pre>`;

        try {
            mermaid.parse(sanitizedContent);
            const status = sanitizationResult.hasReplacements ? 'warning' : 'success';
            const message = sanitizationResult.hasReplacements ? 
                '⚠ Sanitized content parsed successfully (Unicode characters were replaced)' : 
                '✓ Sanitized content parsed successfully!';
            document.getElementById('sanitized-result').innerHTML = `<p class="${status}">${message}</p>`;
            
            // Try to render the sanitized diagram
            const renderDiv = document.createElement('div');
            renderDiv.className = 'mermaid';
            renderDiv.textContent = sanitizedContent;
            document.getElementById('sanitized-result').appendChild(renderDiv);
            
            setTimeout(() => {
                try {
                    mermaid.init(undefined, renderDiv);
                } catch (renderError) {
                    document.getElementById('sanitized-result').innerHTML += `<p class="error">Render error: ${renderError.message}</p>`;
                }
            }, 100);
            
        } catch (error) {
            document.getElementById('sanitized-result').innerHTML = `<p class="error">✗ Sanitized content failed: ${error.message}</p>`;
        }
    </script>
</body>
</html>
